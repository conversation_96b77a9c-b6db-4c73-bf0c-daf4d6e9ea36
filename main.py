from requests import get
from config import * # 导入 config 包中的所有内容，这里的所有是用__all__定义的内容
from controller import cc 
import controller
from controller import *


def main():
    print(cc.gg)  # 输出 '321'
    print(controller.__version__)  # 输出 '0.1.0'
    print(controller.__author__)  # 输出 'Your Name'

    print(aa.abc)  # 输出 '123'
    print(aa.cc) 
    print(ee)  # 没有在config包中__init__.py的__all__输出
    print(controller.ee)  # 引入config的
    def count_up_to(n):
        for i in range(n):
            yield i  # 每次 yield 返回一个值，下次调用从 yield 处继续执行

# 使用生成器
    gen = count_up_to(5)
    for num in gen:
        print(num)  # 依次输出 0, 1, 2, 3, 4

    # 使用 for 循环创建平方数列表
    squares = []
    for x in range(10):
        squares.append(x**2)
    print(squares)

# 使用列表推导式达到同样的效果
    squares_comp = [x**2 for x in range(10)]
    print(squares_comp)

# 加入条件：只计算偶数的平方
    even_squares = [x**2 for x in range(10) if x % 2 == 0]
    print(even_squares)


    make_pizza(12, "蘑菇", "青椒", "香肠")
    user_profile = build_profile('爱因', '斯坦', location='普林斯顿', field='物理')
    print(user_profile)

def build_profile(first, last, **user_info):
    user_info['first_name'] = first
    user_info['last_name'] = last
    return user_info

def make_pizza(size, *toppings):
    print(f"\n制作一个 {size} 寸的披萨，配料如下:")
    for topping in toppings:
        print(f"- {topping}")
    
    
    # for i in range(6):
    #     if i%2 == 0:
    #         testStr('Hello World %d' % i)
    #     elif i%3 == 0:
    #         print('这是3的倍数')
    #     else:
    #         testNum(i)

def testStr(str):
    print('-----------------------------')
    print(type(str))
    print(str)

def testNum(num):
    print('-----------------------------')  
    print(type(num))
    print(num)


if __name__ == "__main__":
    main()
